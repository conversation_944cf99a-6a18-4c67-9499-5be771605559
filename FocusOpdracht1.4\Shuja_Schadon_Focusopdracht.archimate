<?xml version="1.0" encoding="UTF-8"?>
<archimate:model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:archimate="http://www.archimatetool.com/archimate" name="Logistix B.V. - Enterprise Architecture" id="id-logistix-bv-model" version="5.0.0">
  <folder name="Strategy" id="id-strategy-folder" type="strategy"/>
  <folder name="Business" id="id-business-folder" type="business">
    <element xsi:type="archimate:BusinessActor" name="Klant" id="id-klant"/>
    <element xsi:type="archimate:BusinessActor" name="Medewerker" id="id-medewerker"/>
    <element xsi:type="archimate:BusinessProcess" name="Orderverwerking" id="id-orderverwerking"/>
    <element xsi:type="archimate:BusinessProcess" name="Magazijnbeheer" id="id-magazijnbeheer"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantenservice" id="id-klantenservice"/>
    <element xsi:type="archimate:BusinessProcess" name="Order ontvangen" id="id-order-ontvangen"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantgegevens controleren" id="id-klant-controleren"/>
    <element xsi:type="archimate:BusinessProcess" name="Voorraad valideren" id="id-voorraad-valideren"/>
    <element xsi:type="archimate:BusinessProcess" name="Picking en Packing" id="id-picking-packing"/>
    <element xsi:type="archimate:BusinessProcess" name="Verzending" id="id-verzending"/>
    <element xsi:type="archimate:BusinessObject" name="Order" id="id-order"/>
    <element xsi:type="archimate:BusinessObject" name="Klantgegevens" id="id-klantgegevens"/>
  </folder>
  <folder name="Application" id="id-application-folder" type="application">
    <element xsi:type="archimate:ApplicationComponent" name="ERP-systeem" id="id-erp"/>
    <element xsi:type="archimate:ApplicationComponent" name="WMS" id="id-wms"/>
    <element xsi:type="archimate:ApplicationComponent" name="TMS" id="id-tms"/>
    <element xsi:type="archimate:ApplicationComponent" name="CRM-systeem" id="id-crm"/>
    <element xsi:type="archimate:DataObject" name="Orderdata" id="id-orderdata"/>
    <element xsi:type="archimate:DataObject" name="Klantdata" id="id-klantdata"/>
    <element xsi:type="archimate:DataObject" name="Voorraaddata" id="id-voorraaddata"/>
  </folder>
  <folder name="Technology &amp; Physical" id="id-technology-folder" type="technology">
    <element xsi:type="archimate:Node" name="Cloud Platform" id="id-cloud-platform"/>
    <element xsi:type="archimate:Node" name="On-premise Server" id="id-onpremise-server"/>
    <element xsi:type="archimate:CommunicationNetwork" name="Netwerk" id="id-netwerk"/>
  </folder>
  <folder name="Motivation" id="id-motivation-folder" type="motivation"/>
  <folder name="Implementation &amp; Migration" id="id-implementation-folder" type="implementation_migration"/>
  <folder name="Other" id="id-other-folder" type="other"/>
  <folder name="Relations" id="id-relations-folder" type="relations">
    <element xsi:type="archimate:TriggeringRelationship" id="rel-klant-order" source="id-klant" target="id-orderverwerking"/>
    <element xsi:type="archimate:AssignmentRelationship" id="rel-medewerker-order" source="id-medewerker" target="id-orderverwerking"/>
    <element xsi:type="archimate:AssignmentRelationship" id="rel-medewerker-magazijn" source="id-medewerker" target="id-magazijnbeheer"/>
    <element xsi:type="archimate:AssignmentRelationship" id="rel-medewerker-klanten" source="id-medewerker" target="id-klantenservice"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-order-ontvangen" source="id-orderverwerking" target="id-order-ontvangen"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-order-controleren" source="id-orderverwerking" target="id-klant-controleren"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-order-valideren" source="id-orderverwerking" target="id-voorraad-valideren"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-magazijn-picking" source="id-magazijnbeheer" target="id-picking-packing"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-magazijn-verzending" source="id-magazijnbeheer" target="id-verzending"/>
    <element xsi:type="archimate:TriggeringRelationship" id="rel-order-magazijn" source="id-orderverwerking" target="id-magazijnbeheer"/>
    <element xsi:type="archimate:TriggeringRelationship" id="rel-magazijn-klanten" source="id-magazijnbeheer" target="id-klantenservice"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-erp-klant" source="id-erp" target="id-klant-controleren"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-wms-voorraad" source="id-wms" target="id-voorraad-valideren"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-wms-picking" source="id-wms" target="id-picking-packing"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-tms-verzending" source="id-tms" target="id-verzending"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-crm-klanten" source="id-crm" target="id-klantenservice"/>
    <element xsi:type="archimate:AccessRelationship" id="rel-erp-orderdata" source="id-erp" target="id-orderdata"/>
    <element xsi:type="archimate:AccessRelationship" id="rel-erp-klantdata" source="id-erp" target="id-klantdata"/>
    <element xsi:type="archimate:AccessRelationship" id="rel-wms-voorraaddata" source="id-wms" target="id-voorraaddata"/>
    <element xsi:type="archimate:AccessRelationship" id="rel-crm-klantdata" source="id-crm" target="id-klantdata"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-cloud-erp" source="id-cloud-platform" target="id-erp"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-onpremise-wms" source="id-onpremise-server" target="id-wms"/>
    <element xsi:type="archimate:AssociationRelationship" id="rel-netwerk-cloud" source="id-netwerk" target="id-cloud-platform"/>
    <element xsi:type="archimate:AssociationRelationship" id="rel-netwerk-onpremise" source="id-netwerk" target="id-onpremise-server"/>
  </folder>
  <folder name="Views" id="id-views-folder" type="diagrams">
    <element xsi:type="archimate:ArchimateDiagramModel" name="Logistix B.V. - Overzicht" id="id-main-view">
      <child xsi:type="archimate:DiagramObject" id="diag-klant" archimateElement="id-klant">
        <bounds x="50" y="50" width="120" height="55"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-medewerker" archimateElement="id-medewerker">
        <bounds x="200" y="50" width="120" height="55"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-orderverwerking" archimateElement="id-orderverwerking">
        <bounds x="50" y="150" width="200" height="80"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-magazijnbeheer" archimateElement="id-magazijnbeheer">
        <bounds x="300" y="150" width="200" height="80"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-klantenservice" archimateElement="id-klantenservice">
        <bounds x="550" y="150" width="200" height="80"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-erp" archimateElement="id-erp">
        <bounds x="50" y="280" width="120" height="60"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-wms" archimateElement="id-wms">
        <bounds x="200" y="280" width="120" height="60"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-tms" archimateElement="id-tms">
        <bounds x="380" y="280" width="120" height="60"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-crm" archimateElement="id-crm">
        <bounds x="550" y="280" width="120" height="60"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-cloud-platform" archimateElement="id-cloud-platform">
        <bounds x="50" y="380" width="150" height="50"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-onpremise-server" archimateElement="id-onpremise-server">
        <bounds x="250" y="380" width="150" height="50"/>
      </child>
      <child xsi:type="archimate:DiagramObject" id="diag-netwerk" archimateElement="id-netwerk">
        <bounds x="450" y="380" width="250" height="50"/>
      </child>
    </element>
  </folder>
</archimate:model>
