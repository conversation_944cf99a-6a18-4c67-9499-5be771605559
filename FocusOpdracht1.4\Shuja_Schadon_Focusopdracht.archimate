<?xml version="1.0" encoding="UTF-8"?>
<archimate:model xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:archimate="http://www.archimatetool.com/archimate" name="Logistix B.V. - Enterprise Architecture" id="id-logistix-main-model" version="5.0.0">
  <folder name="Strategy" id="id-strategy-folder" type="strategy"/>
  <folder name="Business" id="id-business-folder" type="business">
    <!-- Business Actors -->
    <element xsi:type="archimate:BusinessActor" name="Klant" id="id-klant"/>
    <element xsi:type="archimate:BusinessActor" name="Medewerker" id="id-medewerker"/>
    
    <!-- Business Processes -->
    <element xsi:type="archimate:BusinessProcess" name="Orderverwerking" id="id-orderverwerking"/>
    <element xsi:type="archimate:BusinessProcess" name="Magazijnbeheer" id="id-magazijnbeheer"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantenservice & Communicatie" id="id-klantenservice"/>
    
    <!-- Sub-processes for Orderverwerking -->
    <element xsi:type="archimate:BusinessProcess" name="Order ontvangen" id="id-order-ontvangen"/>
    <element xsi:type="archimate:BusinessProcess" name="Klantgegevens controleren" id="id-klant-controleren"/>
    <element xsi:type="archimate:BusinessProcess" name="Voorraad valideren" id="id-voorraad-valideren"/>
    
    <!-- Sub-processes for Magazijnbeheer -->
    <element xsi:type="archimate:BusinessProcess" name="Picking & Packing" id="id-picking-packing"/>
    <element xsi:type="archimate:BusinessProcess" name="Verzending" id="id-verzending"/>
    
    <!-- Sub-processes for Klantenservice -->
    <element xsi:type="archimate:BusinessProcess" name="Klantvragen behandelen" id="id-klantvragen"/>
    <element xsi:type="archimate:BusinessProcess" name="Order status opvragen" id="id-order-status"/>
    
    <!-- Business Objects -->
    <element xsi:type="archimate:BusinessObject" name="Order" id="id-order"/>
    <element xsi:type="archimate:BusinessObject" name="Klantgegevens" id="id-klantgegevens"/>
    <element xsi:type="archimate:BusinessObject" name="Picklijst" id="id-picklijst"/>
    <element xsi:type="archimate:BusinessObject" name="Verzendlabel" id="id-verzendlabel"/>
  </folder>
  
  <folder name="Application" id="id-application-folder" type="application">
    <!-- Application Components -->
    <element xsi:type="archimate:ApplicationComponent" name="ERP-systeem" id="id-erp"/>
    <element xsi:type="archimate:ApplicationComponent" name="WMS" id="id-wms"/>
    <element xsi:type="archimate:ApplicationComponent" name="TMS" id="id-tms"/>
    <element xsi:type="archimate:ApplicationComponent" name="CRM-systeem" id="id-crm"/>
    
    <!-- Application Services -->
    <element xsi:type="archimate:ApplicationService" name="Klantbeheer" id="id-klantbeheer-service"/>
    <element xsi:type="archimate:ApplicationService" name="Voorraadbeheer" id="id-voorraadbeheer-service"/>
    <element xsi:type="archimate:ApplicationService" name="Transportbeheer" id="id-transportbeheer-service"/>
    <element xsi:type="archimate:ApplicationService" name="Klantcontact" id="id-klantcontact-service"/>
    
    <!-- Data Objects -->
    <element xsi:type="archimate:DataObject" name="Orderdata" id="id-orderdata"/>
    <element xsi:type="archimate:DataObject" name="Klantdata" id="id-klantdata"/>
    <element xsi:type="archimate:DataObject" name="Voorraaddata" id="id-voorraaddata"/>
    <element xsi:type="archimate:DataObject" name="Transportdata" id="id-transportdata"/>
  </folder>
  
  <folder name="Technology &amp; Physical" id="id-technology-folder" type="technology">
    <element xsi:type="archimate:Node" name="Cloud Platform" id="id-cloud-platform"/>
    <element xsi:type="archimate:Node" name="On-premise Server" id="id-onpremise-server"/>
    <element xsi:type="archimate:Device" name="Werkstation" id="id-werkstation"/>
    <element xsi:type="archimate:CommunicationNetwork" name="Netwerk" id="id-netwerk"/>
  </folder>
  
  <folder name="Motivation" id="id-motivation-folder" type="motivation"/>
  <folder name="Implementation &amp; Migration" id="id-implementation-folder" type="implementation_migration"/>
  <folder name="Other" id="id-other-folder" type="other"/>
  
  <folder name="Relations" id="id-relations-folder" type="relations">
    <!-- Business Process Compositions -->
    <element xsi:type="archimate:CompositionRelationship" id="rel-orderverwerking-ontvangen" source="id-orderverwerking" target="id-order-ontvangen"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-orderverwerking-controleren" source="id-orderverwerking" target="id-klant-controleren"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-orderverwerking-valideren" source="id-orderverwerking" target="id-voorraad-valideren"/>
    
    <element xsi:type="archimate:CompositionRelationship" id="rel-magazijn-picking" source="id-magazijnbeheer" target="id-picking-packing"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-magazijn-verzending" source="id-magazijnbeheer" target="id-verzending"/>
    
    <element xsi:type="archimate:CompositionRelationship" id="rel-klanten-vragen" source="id-klantenservice" target="id-klantvragen"/>
    <element xsi:type="archimate:CompositionRelationship" id="rel-klanten-status" source="id-klantenservice" target="id-order-status"/>
    
    <!-- Actor-Process Relations -->
    <element xsi:type="archimate:TriggeringRelationship" id="rel-klant-order" source="id-klant" target="id-orderverwerking"/>
    <element xsi:type="archimate:AssignmentRelationship" id="rel-medewerker-orderverwerking" source="id-medewerker" target="id-orderverwerking"/>
    <element xsi:type="archimate:AssignmentRelationship" id="rel-medewerker-magazijn" source="id-medewerker" target="id-magazijnbeheer"/>
    <element xsi:type="archimate:AssignmentRelationship" id="rel-medewerker-klanten" source="id-medewerker" target="id-klantenservice"/>
    
    <!-- Process Flow Relations -->
    <element xsi:type="archimate:TriggeringRelationship" id="rel-order-magazijn" source="id-orderverwerking" target="id-magazijnbeheer"/>
    <element xsi:type="archimate:TriggeringRelationship" id="rel-magazijn-klanten" source="id-magazijnbeheer" target="id-klantenservice"/>
    
    <!-- Application-Process Realizations -->
    <element xsi:type="archimate:RealizationRelationship" id="rel-erp-klant-controleren" source="id-erp" target="id-klant-controleren"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-wms-voorraad" source="id-wms" target="id-voorraad-valideren"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-wms-picking" source="id-wms" target="id-picking-packing"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-tms-verzending" source="id-tms" target="id-verzending"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-crm-klantvragen" source="id-crm" target="id-klantvragen"/>
    
    <!-- Application Services -->
    <element xsi:type="archimate:RealizationRelationship" id="rel-erp-klantbeheer" source="id-erp" target="id-klantbeheer-service"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-wms-voorraad-service" source="id-wms" target="id-voorraadbeheer-service"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-tms-transport-service" source="id-tms" target="id-transportbeheer-service"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-crm-contact-service" source="id-crm" target="id-klantcontact-service"/>
    
    <!-- Data Access Relations -->
    <element xsi:type="archimate:AccessRelationship" id="rel-erp-orderdata" source="id-erp" target="id-orderdata"/>
    <element xsi:type="archimate:AccessRelationship" id="rel-erp-klantdata" source="id-erp" target="id-klantdata"/>
    <element xsi:type="archimate:AccessRelationship" id="rel-wms-voorraaddata" source="id-wms" target="id-voorraaddata"/>
    <element xsi:type="archimate:AccessRelationship" id="rel-tms-transportdata" source="id-tms" target="id-transportdata"/>
    <element xsi:type="archimate:AccessRelationship" id="rel-crm-klantdata" source="id-crm" target="id-klantdata"/>
    
    <!-- Technology Relations -->
    <element xsi:type="archimate:RealizationRelationship" id="rel-cloud-erp" source="id-cloud-platform" target="id-erp"/>
    <element xsi:type="archimate:RealizationRelationship" id="rel-onpremise-wms" source="id-onpremise-server" target="id-wms"/>
    <element xsi:type="archimate:AssociationRelationship" id="rel-netwerk-cloud" source="id-netwerk" target="id-cloud-platform"/>
    <element xsi:type="archimate:AssociationRelationship" id="rel-netwerk-onpremise" source="id-netwerk" target="id-onpremise-server"/>
  </folder>

  <folder name="Views" id="id-views-folder" type="diagrams">
    <element xsi:type="archimate:ArchimateDiagramModel" name="Logistix B.V. - Overzicht" id="id-main-view">
      <!-- Business Layer Objects -->
      <child xsi:type="archimate:DiagramObject" id="diag-klant" archimateElement="id-klant">
        <bounds x="50" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-klant-order" source="diag-klant" target="diag-orderverwerking" archimateRelationship="rel-klant-order"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="diag-medewerker" archimateElement="id-medewerker">
        <bounds x="200" y="50" width="120" height="55"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-medewerker-order" source="diag-medewerker" target="diag-orderverwerking" archimateRelationship="rel-medewerker-orderverwerking"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-medewerker-magazijn" source="diag-medewerker" target="diag-magazijnbeheer" archimateRelationship="rel-medewerker-magazijn"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-medewerker-klanten" source="diag-medewerker" target="diag-klantenservice" archimateRelationship="rel-medewerker-klanten"/>
      </child>

      <!-- Business Processes -->
      <child xsi:type="archimate:DiagramObject" id="diag-orderverwerking" targetConnections="conn-klant-order conn-medewerker-order" archimateElement="id-orderverwerking">
        <bounds x="50" y="150" width="200" height="80"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-order-magazijn" source="diag-orderverwerking" target="diag-magazijnbeheer" archimateRelationship="rel-order-magazijn"/>

        <!-- Sub-processes -->
        <child xsi:type="archimate:DiagramObject" id="diag-order-ontvangen" archimateElement="id-order-ontvangen">
          <bounds x="10" y="20" width="50" height="25"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="diag-klant-controleren" targetConnections="conn-erp-klant" archimateElement="id-klant-controleren">
          <bounds x="70" y="20" width="50" height="25"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="diag-voorraad-valideren" targetConnections="conn-wms-voorraad" archimateElement="id-voorraad-valideren">
          <bounds x="130" y="20" width="50" height="25"/>
        </child>
      </child>

      <child xsi:type="archimate:DiagramObject" id="diag-magazijnbeheer" targetConnections="conn-order-magazijn conn-medewerker-magazijn" archimateElement="id-magazijnbeheer">
        <bounds x="300" y="150" width="200" height="80"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-magazijn-klanten" source="diag-magazijnbeheer" target="diag-klantenservice" archimateRelationship="rel-magazijn-klanten"/>

        <!-- Sub-processes -->
        <child xsi:type="archimate:DiagramObject" id="diag-picking-packing" targetConnections="conn-wms-picking" archimateElement="id-picking-packing">
          <bounds x="20" y="20" width="70" height="25"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="diag-verzending" targetConnections="conn-tms-verzending" archimateElement="id-verzending">
          <bounds x="110" y="20" width="70" height="25"/>
        </child>
      </child>

      <child xsi:type="archimate:DiagramObject" id="diag-klantenservice" targetConnections="conn-magazijn-klanten conn-medewerker-klanten" archimateElement="id-klantenservice">
        <bounds x="550" y="150" width="200" height="80"/>

        <!-- Sub-processes -->
        <child xsi:type="archimate:DiagramObject" id="diag-klantvragen" targetConnections="conn-crm-klantvragen" archimateElement="id-klantvragen">
          <bounds x="20" y="20" width="70" height="25"/>
        </child>
        <child xsi:type="archimate:DiagramObject" id="diag-order-status" archimateElement="id-order-status">
          <bounds x="110" y="20" width="70" height="25"/>
        </child>
      </child>

      <!-- Application Layer -->
      <child xsi:type="archimate:DiagramObject" id="diag-erp" archimateElement="id-erp">
        <bounds x="50" y="280" width="120" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-erp-klant" source="diag-erp" target="diag-klant-controleren" archimateRelationship="rel-erp-klant-controleren"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="diag-wms" archimateElement="id-wms">
        <bounds x="200" y="280" width="120" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-wms-voorraad" source="diag-wms" target="diag-voorraad-valideren" archimateRelationship="rel-wms-voorraad"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-wms-picking" source="diag-wms" target="diag-picking-packing" archimateRelationship="rel-wms-picking"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="diag-tms" archimateElement="id-tms">
        <bounds x="380" y="280" width="120" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-tms-verzending" source="diag-tms" target="diag-verzending" archimateRelationship="rel-tms-verzending"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="diag-crm" archimateElement="id-crm">
        <bounds x="550" y="280" width="120" height="60"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-crm-klantvragen" source="diag-crm" target="diag-klantvragen" archimateRelationship="rel-crm-klantvragen"/>
      </child>

      <!-- Technology Layer -->
      <child xsi:type="archimate:DiagramObject" id="diag-cloud-platform" archimateElement="id-cloud-platform">
        <bounds x="50" y="380" width="150" height="50"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-cloud-erp" source="diag-cloud-platform" target="diag-erp" archimateRelationship="rel-cloud-erp"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="diag-onpremise-server" archimateElement="id-onpremise-server">
        <bounds x="250" y="380" width="150" height="50"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-onpremise-wms" source="diag-onpremise-server" target="diag-wms" archimateRelationship="rel-onpremise-wms"/>
      </child>

      <child xsi:type="archimate:DiagramObject" id="diag-netwerk" archimateElement="id-netwerk">
        <bounds x="450" y="380" width="250" height="50"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-netwerk-cloud" source="diag-netwerk" target="diag-cloud-platform" archimateRelationship="rel-netwerk-cloud"/>
        <sourceConnection xsi:type="archimate:Connection" id="conn-netwerk-onpremise" source="diag-netwerk" target="diag-onpremise-server" archimateRelationship="rel-netwerk-onpremise"/>
      </child>
    </element>
  </folder>
</archimate:model>
